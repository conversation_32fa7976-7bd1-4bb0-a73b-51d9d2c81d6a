import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Grouped Image Mapping Fix Tests', () {
    test('should generate different hashes for different messages', () {
      // Test that different message IDs and URLs generate different hashes
      final message1Hash = '1759729404602473_https://example.com/image1.jpg'.hashCode.abs();
      final message2Hash = '1759729409955974_https://example.com/image2.jpg'.hashCode.abs();
      final message3Hash = '1759729413543969_https://example.com/image3.jpg'.hashCode.abs();
      final message4Hash = '1759729417008792_https://example.com/image4.jpg'.hashCode.abs();

      // All hashes should be different
      expect(message1Hash, isNot(equals(message2Hash)));
      expect(message1Hash, isNot(equals(message3Hash)));
      expect(message1Hash, isNot(equals(message4Hash)));
      expect(message2Hash, isNot(equals(message3Hash)));
      expect(message2Hash, isNot(equals(message4Hash)));
      expect(message3Hash, isNot(equals(message4Hash)));

      print('Message 1 hash: $message1Hash');
      print('Message 2 hash: $message2Hash');
      print('Message 3 hash: $message3Hash');
      print('Message 4 hash: $message4Hash');
    });

    test('should generate consistent hashes for same message', () {
      // Test that the same message always generates the same hash
      final messageId = '1759729404602473';
      final mediaUrl = 'https://example.com/image1.jpg';
      
      final hash1 = '${messageId}_$mediaUrl'.hashCode.abs();
      final hash2 = '${messageId}_$mediaUrl'.hashCode.abs();
      final hash3 = '${messageId}_$mediaUrl'.hashCode.abs();

      // All hashes should be identical
      expect(hash1, equals(hash2));
      expect(hash1, equals(hash3));
      expect(hash2, equals(hash3));

      print('Consistent hash: $hash1');
    });

    test('should map to different indices with sufficient image files', () {
      // Simulate having 27 image files (as seen in logs)
      const totalImages = 27;
      
      // Test different message hashes
      final hashes = [
        571648257, // From logs
        260066700, // From logs
        481607100, // From logs
        102522441, // From logs
        262766096, // From logs
        569354435, // From logs
        503968096, // From logs
        147174808, // From logs
      ];

      final indices = hashes.map((hash) => hash % totalImages).toList();
      
      print('Hash to index mapping:');
      for (int i = 0; i < hashes.length; i++) {
        print('Hash ${hashes[i]} -> Index ${indices[i]}');
      }

      // Check that we get different indices for different hashes
      final uniqueIndices = indices.toSet();
      expect(uniqueIndices.length, greaterThan(1), 
        reason: 'Should map to different indices for different hashes');
    });

    test('should handle edge cases correctly', () {
      // Test with empty or null values
      final emptyHash = '_'.hashCode.abs();
      final nullHash = 'null_null'.hashCode.abs();
      
      expect(emptyHash, isA<int>());
      expect(nullHash, isA<int>());
      
      // Test with very long strings
      final longString = 'a' * 1000;
      final longHash = '${longString}_${longString}'.hashCode.abs();
      expect(longHash, isA<int>());
      
      print('Empty hash: $emptyHash');
      print('Null hash: $nullHash');
      print('Long string hash: $longHash');
    });

    test('should demonstrate the fix for grouped images issue', () {
      // Simulate the original problem: all groups showing same images
      // With round-robin approach (old logic)
      const totalImages = 27;
      
      // Group 1: indices 0, 1, 2, 3 (round-robin)
      final group1RoundRobin = [0, 1, 2, 3];
      
      // Group 2: indices 0, 1, 2, 3 (round-robin) - SAME AS GROUP 1!
      final group2RoundRobin = [0, 1, 2, 3];
      
      // With hash-based approach (new logic)
      final group1Messages = [
        '1759729404602473_https://example.com/image1.jpg',
        '1759729409955974_https://example.com/image2.jpg',
        '1759729413543969_https://example.com/image3.jpg',
        '1759729417008792_https://example.com/image4.jpg',
      ];
      
      final group2Messages = [
        '1759723477465590_https://example.com/image5.jpg',
        '1759723477473527_https://example.com/image6.jpg',
        '1759723480862437_https://example.com/image7.jpg',
        '1759723481421949_https://example.com/image8.jpg',
      ];
      
      final group1HashBased = group1Messages
          .map((msg) => msg.hashCode.abs() % totalImages)
          .toList();
      
      final group2HashBased = group2Messages
          .map((msg) => msg.hashCode.abs() % totalImages)
          .toList();
      
      print('Round-robin approach:');
      print('Group 1: $group1RoundRobin');
      print('Group 2: $group2RoundRobin');
      print('Problem: Both groups show same images!');
      
      print('\nHash-based approach:');
      print('Group 1: $group1HashBased');
      print('Group 2: $group2HashBased');
      
      // Verify that groups are different with hash-based approach
      expect(group1HashBased, isNot(equals(group2HashBased)),
        reason: 'Hash-based approach should produce different image mappings for different groups');
      
      // Verify that round-robin produces identical results (the problem)
      expect(group1RoundRobin, equals(group2RoundRobin),
        reason: 'Round-robin approach produces identical mappings (the original problem)');
    });
  });
}
