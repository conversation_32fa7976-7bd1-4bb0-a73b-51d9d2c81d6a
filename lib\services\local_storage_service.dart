import 'dart:io';
// import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:flutter_image_compress/flutter_image_compress.dart';

/// Local storage service for WhatsApp-like image management
/// Handles local file storage, thumbnails, and cache management
class LocalStorageService {
  static LocalStorageService? _instance;
  static LocalStorageService get instance =>
      _instance ??= LocalStorageService._();
  LocalStorageService._();

  static Directory? _appDocumentsDir;
  static Directory? _chatImagesDir;
  static Directory? _thumbnailsDir;
  static Directory? _tempDir;

  /// Initialize local storage directories
  static Future<void> initialize() async {
    try {
      _appDocumentsDir = await getApplicationDocumentsDirectory();

      // Create chat images directory
      _chatImagesDir = Directory(
        path.join(_appDocumentsDir!.path, 'chat_images'),
      );
      if (!await _chatImagesDir!.exists()) {
        await _chatImagesDir!.create(recursive: true);
      }

      // Create thumbnails directory
      _thumbnailsDir = Directory(
        path.join(_appDocumentsDir!.path, 'thumbnails'),
      );
      if (!await _thumbnailsDir!.exists()) {
        await _thumbnailsDir!.create(recursive: true);
      }

      // Create temp directory
      _tempDir = Directory(path.join(_appDocumentsDir!.path, 'temp'));
      if (!await _tempDir!.exists()) {
        await _tempDir!.create(recursive: true);
      }

      // Clean up old files on initialization
      await _cleanupOldFiles();

      debugPrint('LocalStorageService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing LocalStorageService: $e');
      rethrow;
    }
  }

  /// Store image locally and generate thumbnail
  static Future<LocalImageResult> storeImageLocally({
    required File sourceFile,
    required String messageId,
    required String chatId,
    int thumbnailSize = 200,
    int imageQuality = 85,
  }) async {
    try {
      if (_chatImagesDir == null || _thumbnailsDir == null) {
        await initialize();
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(sourceFile.path).toLowerCase();
      final fileName = '${chatId}_${messageId}_$timestamp$extension';

      // Store original/compressed image
      final localImagePath = path.join(_chatImagesDir!.path, fileName);
      final compressedFile = await _compressImage(
        sourceFile,
        localImagePath,
        quality: imageQuality,
      );

      // Generate thumbnail
      final thumbnailFileName = 'thumb_$fileName';
      final thumbnailPath = path.join(_thumbnailsDir!.path, thumbnailFileName);
      final thumbnailFile = await _generateThumbnail(
        compressedFile,
        thumbnailPath,
      );

      // Get file sizes
      final imageSize = await compressedFile.length();
      final thumbnailSize = await thumbnailFile.length();

      return LocalImageResult(
        localImagePath: compressedFile.path,
        thumbnailPath: thumbnailFile.path,
        imageSize: imageSize,
        thumbnailSize: thumbnailSize,
        success: true,
      );
    } catch (e) {
      debugPrint('Error storing image locally: $e');
      return LocalImageResult(success: false, error: e.toString());
    }
  }

  /// Compress image for local storage
  static Future<File> _compressImage(
    File sourceFile,
    String targetPath, {
    int quality = 85,
  }) async {
    try {
      final compressedFile = await FlutterImageCompress.compressAndGetFile(
        sourceFile.path,
        targetPath,
        quality: quality,
        minWidth: 1024,
        minHeight: 1024,
        format: CompressFormat.jpeg,
        keepExif: false,
      );

      if (compressedFile != null) {
        return File(compressedFile.path);
      } else {
        // If compression fails, copy original file
        return await sourceFile.copy(targetPath);
      }
    } catch (e) {
      // Fallback to copying original file
      return await sourceFile.copy(targetPath);
    }
  }

  /// Generate thumbnail for quick preview
  static Future<File> _generateThumbnail(
    File sourceFile,
    String thumbnailPath, {
    int size = 200,
  }) async {
    try {
      final thumbnailFile = await FlutterImageCompress.compressAndGetFile(
        sourceFile.path,
        thumbnailPath,
        quality: 60,
        minWidth: size,
        minHeight: size,
        format: CompressFormat.jpeg,
        keepExif: false,
      );

      if (thumbnailFile != null) {
        return File(thumbnailFile.path);
      } else {
        // If thumbnail generation fails, copy original
        return await sourceFile.copy(thumbnailPath);
      }
    } catch (e) {
      // Fallback to copying original file
      return await sourceFile.copy(thumbnailPath);
    }
  }

  /// Check if local file exists
  static Future<bool> localFileExists(String? localPath) async {
    if (localPath == null || localPath.isEmpty) return false;
    try {
      final file = File(localPath);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  /// Get local file if it exists
  static Future<File?> getLocalFile(String? localPath) async {
    if (await localFileExists(localPath)) {
      return File(localPath!);
    }
    return null;
  }

  /// Delete local image and thumbnail
  static Future<bool> deleteLocalImage(
    String? localPath,
    String? thumbnailPath,
  ) async {
    try {
      bool success = true;

      if (localPath != null && await localFileExists(localPath)) {
        await File(localPath).delete();
      }

      if (thumbnailPath != null && await localFileExists(thumbnailPath)) {
        await File(thumbnailPath).delete();
      }

      return success;
    } catch (e) {
      debugPrint('Error deleting local image: $e');
      return false;
    }
  }

  /// Clean up old files (older than 30 days)
  static Future<void> _cleanupOldFiles() async {
    try {
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));

      // Clean chat images
      if (_chatImagesDir != null && await _chatImagesDir!.exists()) {
        await _cleanupDirectory(_chatImagesDir!, cutoffDate);
      }

      // Clean thumbnails
      if (_thumbnailsDir != null && await _thumbnailsDir!.exists()) {
        await _cleanupDirectory(_thumbnailsDir!, cutoffDate);
      }

      // Clean temp files
      if (_tempDir != null && await _tempDir!.exists()) {
        await _cleanupDirectory(_tempDir!, cutoffDate);
      }

      debugPrint('Local storage cleanup completed');
    } catch (e) {
      debugPrint('Error during cleanup: $e');
    }
  }

  /// Clean up files in a directory older than cutoff date
  static Future<void> _cleanupDirectory(
    Directory dir,
    DateTime cutoffDate,
  ) async {
    try {
      final files = dir.listSync();
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          if (stat.modified.isBefore(cutoffDate)) {
            await file.delete();
          }
        }
      }
    } catch (e) {
      debugPrint('Error cleaning directory ${dir.path}: $e');
    }
  }

  /// Get cache size information
  static Future<CacheInfo> getCacheInfo() async {
    try {
      int totalSize = 0;
      int fileCount = 0;

      if (_chatImagesDir != null && await _chatImagesDir!.exists()) {
        final result = await _getDirectorySize(_chatImagesDir!);
        totalSize += result.size;
        fileCount += result.count;
      }

      if (_thumbnailsDir != null && await _thumbnailsDir!.exists()) {
        final result = await _getDirectorySize(_thumbnailsDir!);
        totalSize += result.size;
        fileCount += result.count;
      }

      return CacheInfo(
        totalSize: totalSize,
        fileCount: fileCount,
        formattedSize: _formatBytes(totalSize),
      );
    } catch (e) {
      return CacheInfo(totalSize: 0, fileCount: 0, formattedSize: '0 B');
    }
  }

  /// Get directory size and file count
  static Future<DirectoryInfo> _getDirectorySize(Directory dir) async {
    int size = 0;
    int count = 0;

    try {
      final files = dir.listSync(recursive: true);
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          size += stat.size;
          count++;
        }
      }
    } catch (e) {
      debugPrint('Error getting directory size: $e');
    }

    return DirectoryInfo(size: size, count: count);
  }  

  /// Format bytes to human readable string
  static String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Clear all cached files
  static Future<bool> clearAllCache() async {
    try {
      if (_chatImagesDir != null && await _chatImagesDir!.exists()) {
        await _chatImagesDir!.delete(recursive: true);
        await _chatImagesDir!.create(recursive: true);
      }

      if (_thumbnailsDir != null && await _thumbnailsDir!.exists()) {
        await _thumbnailsDir!.delete(recursive: true);
        await _thumbnailsDir!.create(recursive: true);
      }

      if (_tempDir != null && await _tempDir!.exists()) {
        await _tempDir!.delete(recursive: true);
        await _tempDir!.create(recursive: true);
      }

      return true;
    } catch (e) {
      debugPrint('Error clearing cache: $e');
      return false;
    }
  }
}

/// Result of storing an image locally
class LocalImageResult {
  final String? localImagePath;
  final String? thumbnailPath;
  final int? imageSize;
  final int? thumbnailSize;
  final bool success;
  final String? error;

  LocalImageResult({
    this.localImagePath,
    this.thumbnailPath,
    this.imageSize,
    this.thumbnailSize,
    required this.success,
    this.error,
  });
}

/// Cache information
class CacheInfo {
  final int totalSize;
  final int fileCount;
  final String formattedSize;

  CacheInfo({
    required this.totalSize,
    required this.fileCount,
    required this.formattedSize,
  });
}

/// Directory information
class DirectoryInfo {
  final int size;
  final int count;

  DirectoryInfo({required this.size, required this.count});
}
