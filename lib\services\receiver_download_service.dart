import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/message.dart';
import '../services/whatsapp_local_storage_service.dart';
import '../services/chat_service.dart';

/// Service to handle optional image downloads for receivers
class ReceiverDownloadService {
  static final Map<String, bool> _downloadingMessages = {};
  static final Map<String, double> _downloadProgress = {};

  /// Check if message is currently downloading
  static bool isDownloading(String messageId) {
    return _downloadingMessages[messageId] ?? false;
  }

  /// Get download progress for a message
  static double getDownloadProgress(String messageId) {
    return _downloadProgress[messageId] ?? 0.0;
  }

  /// Download image for receiver with progress tracking
  static Future<bool> downloadImageForReceiver({
    required String messageId,
    required String chatId,
    required String downloadUrl,
    required String senderId,
    required Function(double) onProgress,
  }) async {
    try {
      _downloadingMessages[messageId] = true;
      _downloadProgress[messageId] = 0.0;
      onProgress(0.0);

      // Initialize WhatsApp local storage
      await WhatsAppLocalStorageService.initialize();
      onProgress(0.1);

      // Store image as receiver with progress tracking
      final receiverResult = await WhatsAppLocalStorageService.storeImageAsReceiver(
        downloadUrl: downloadUrl,
        messageId: messageId,
        chatId: chatId,
        senderId: senderId,
      );
      onProgress(0.8);

      if (receiverResult.success) {
        // Update message with local paths in Firestore
        await ChatService.updateMessageWithLocalPaths(
          chatId: chatId,
          messageId: messageId,
          localImagePath: receiverResult.localImagePath!,
          localThumbnailPath: receiverResult.thumbnailPath,
        );
        onProgress(1.0);

        debugPrint(' Image downloaded successfully: ${receiverResult.encryptedFileName}');
        return true;
      } else {
        debugPrint('❌ Download failed: ${receiverResult.error}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error downloading image: $e');
      return false;
    } finally {
      _downloadingMessages.remove(messageId);
      _downloadProgress.remove(messageId);
    }
  }

  /// Show download dialog for image message
  static Future<bool> showDownloadDialog({
    required BuildContext context,
    required Message message,
    required String chatId,
  }) async {
    if (message.mediaUrl == null) return false;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Download Image'),
        content: const Text(
          'This image will be downloaded and saved to your device. Do you want to continue?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF005368),
              foregroundColor: Colors.white,
            ),
            child: const Text('Download'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// Show download progress dialog
  static Future<bool> showDownloadProgressDialog({
    required BuildContext context,
    required Message message,
    required String chatId,
  }) async {
    if (message.mediaUrl == null) return false;

    bool downloadSuccess = false;
    
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: const Text('Downloading Image'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  'Downloading image...',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          );
        },
      ),
    );

    // Start download in background
    downloadSuccess = await downloadImageForReceiver( 
      messageId: message.id,
      chatId: chatId,
      downloadUrl: message.mediaUrl!,
      senderId: message.senderId,
      onProgress: (progress) {
        // Progress updates handled in the dialog
      },
    );

    return downloadSuccess;
  }

  /// Check if image needs download option (not downloaded yet)
  static Future<bool> needsDownload(Message message) async {
    if (message.type != MessageType.image || message.mediaUrl == null) {
      return false;
    }

    // Check if already has local image
    if (message.hasLocalImage) {
      final file = File(message.localImagePath!);
      if (await file.exists()) {
        return false; // Already downloaded
      }
    }

    return true; // Needs download
  }

  /// Get download status for a message
  static Future<DownloadStatus> getDownloadStatus(Message message) async {
    if (isDownloading(message.id)) {
      return DownloadStatus.downloading;
    }

    if (await needsDownload(message)) {
      return DownloadStatus.notDownloaded;
    }

    return DownloadStatus.downloaded;
  }
}

/// Download status enum
enum DownloadStatus {
  notDownloaded,
  downloading,
  downloaded,
}

/// Provider for download status
final downloadStatusProvider = StateProvider.family<DownloadStatus, String>((ref, messageId) {
  return DownloadStatus.notDownloaded;
});

/// Provider for download progress
final downloadProgressProvider = StateProvider.family<double, String>((ref, messageId) {
  return 0.0;
});
